{"name": "podium", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "format": "biome format --write"}, "dependencies": {"@hono/zod-validator": "^0.7.3", "@hookform/resolvers": "^5.2.2", "@icons-pack/react-simple-icons": "^13.7.0", "@prisma/client": "6.16.2", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-query": "^5.89.0", "better-auth": "^1.3.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "hono": "^4.9.7", "lucide-react": "^0.544.0", "next": "15.5.3", "next-themes": "^0.4.6", "ogl": "^1.0.11", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "zod": "^4.1.9"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "prisma": "6.16.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.8", "typescript": "^5"}}