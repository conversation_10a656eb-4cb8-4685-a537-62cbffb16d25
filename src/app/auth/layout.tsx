import { Aurora } from "@/components/bits/aurora";

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="container mx-auto min-h-svh grid grid-cols-2 items-center">
      {children}
      <div className="absolute inset-0 -z-10">
        <div className="bg-gradient-to-r from-background to-transparent absolute inset-0" />
        <Aurora
          colorStops={["#D23E27", "#F8D532", "#1AB2C4"]}
          blend={1}
          amplitude={1.0}
          speed={0.5}
        />
      </div>
    </div>
  );
}
