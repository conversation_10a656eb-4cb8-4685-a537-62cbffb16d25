"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { SiGith<PERSON>, SiGoogle } from "@icons-pack/react-simple-icons";
import { useRouter } from "next/navigation";
import { type FC, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { authClient } from "@/lib/auth-client";

const formSchema = z.object({
  email: z.email({ message: "Must be a valid email." }),
  password: z.string(),
  rememberMe: z.boolean().optional(),
});

export const SignInForm: FC = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: true,
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    setError("");

    try {
      const { data, error } = await authClient.signIn.email({
        email: values.email,
        password: values.password,
        rememberMe: true,
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!data?.user) {
        throw new Error("Failed to sign in");
      }

      router.push("/dashboard");
      router.refresh();
      setIsLoading(false);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred",
      );
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className={"space-y-12"}>
          <fieldset className="space-y-4" disabled={isLoading}>
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  {/* <FormLabel>Email</FormLabel> */}
                  <FormControl>
                    <Input
                      placeholder="Email"
                      type="email"
                      autoComplete="webauthn"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  {/* <FormLabel>Password</FormLabel> */}
                  <FormControl>
                    <Input
                      placeholder="Password"
                      type="password"
                      autoComplete="webauthn"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="flex items-center space-x-2">
                      <Checkbox {...field} />
                      <FormLabel>Remember me</FormLabel>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {error && (
              <Badge
                variant={"destructive"}
                className="text-destructive-foreground"
              >
                {error}
              </Badge>
            )}
          </fieldset>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Logging in..." : "Log in to your account"}
          </Button>
        </form>
      </Form>
      <div className="flex items-center gap-x-2 py-8">
        <Separator className="flex-1" />
        <span className="text-muted-foreground text-xs">or</span>
        <Separator className="flex-1" />
      </div>
      <div className="flex flex-col gap-2">
        <Button
          variant={"outline"}
          onClick={() => {
            authClient.signIn.social({ provider: "github" });
          }}
        >
          <SiGithub />
          Continue with Github
        </Button>
        <Button
          variant={"outline"}
          onClick={() => {
            authClient.signIn.social({ provider: "google" });
          }}
        >
          <SiGoogle />
          Continue with Google
        </Button>
      </div>
    </div>
  );
};
