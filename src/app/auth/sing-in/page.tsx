import type { NextPage } from "next";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { auth } from "@/lib/auth";
import { SignInForm } from "./sign-in-form";

const LoginPage: NextPage = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (session) {
    return redirect("/");
  }

  return (
    <aside className="w-fit max-w-sm space-y-12">
      <div className="space-y-1.5">
        <div className="text-xl">Log in to Podium&trade;</div>
        <p className="text-sm text-muted-foreground text-balance">
          Log in to your account to keep scaling your podcasts.
        </p>
      </div>
      <SignInForm />
    </aside>
  );
};

export default LoginPage;
