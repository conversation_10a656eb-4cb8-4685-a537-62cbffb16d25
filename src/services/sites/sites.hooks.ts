"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { z } from "zod";
import { apiClient } from "@/lib/api-client";
import type {
  SiteCreateInput as SiteCreateInputSchema,
  SiteUpdateInput as SiteUpdateInputSchema,
} from "@/services/sites/sites.schema";
import type { DTO, PublicDTO } from "./sites.types";

// Infer input types from your Zod schemas (for client typing only)
export type SiteCreateInput = z.infer<typeof SiteCreateInputSchema>;
export type SiteUpdateInput = z.infer<typeof SiteUpdateInputSchema>;

// DTO coming back from the API (dates as ISO strings)
export type PrivateDTO = DTO;

type ListResp = { items: PrivateDTO[]; total: number };

async function jsonOrThrow<T>(resPromise: Promise<Response>): Promise<T> {
  const res = await resPromise;
  if (!res.ok) {
    const msg = await res.text().catch(() => "");
    throw new Error(msg || `HTTP ${res.status}`);
  }
  // NOTE: only call this where you expect a JSON body (not for 204)
  return res.json() as Promise<T>;
}

const keys = {
  all: ["sites"] as const,
  list: (q?: string) => [...keys.all, "list", q ?? ""] as const,
  count: () => [...keys.all, "count"] as const,
  detail: (id: string) => [...keys.all, "detail", id] as const,
  bySub: (sub: string) => [...keys.all, "sub", sub] as const,
};

function useAll(params?: { q?: string }) {
  return useQuery({
    queryKey: keys.list(params?.q),
    queryFn: () =>
      jsonOrThrow<ListResp>(apiClient.api.sites.$get({ query: params ?? {} })),
    staleTime: 15_000,
  });
}

function useCount() {
  return useQuery({
    queryKey: keys.count(),
    queryFn: () =>
      jsonOrThrow<{ total: number }>(apiClient.api.sites.count.$get()).then(
        (r) => r.total,
      ),
    staleTime: 60_000,
  });
}

function useGet(id?: string) {
  return useQuery({
    queryKey: id ? keys.detail(id) : ["sites", "detail", "nil"],
    queryFn: () =>
      jsonOrThrow<PrivateDTO>(
        apiClient.api.sites[":id"].$get({ param: { id: id! } }),
      ),
    enabled: !!id,
    staleTime: 30_000,
  });
}

function useGetBySubdomain(sub?: string) {
  return useQuery({
    queryKey: sub ? keys.bySub(sub) : ["sites", "sub", "nil"],
    queryFn: () =>
      jsonOrThrow<PublicDTO>(
        apiClient.api.sites["by-subdomain"][":sub"].$get({
          param: { sub: sub! },
        }),
      ),
    enabled: !!sub,
  });
}

function useCreate() {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: ({ data }: { data: SiteCreateInput }) =>
      jsonOrThrow<PrivateDTO>(apiClient.api.sites.$post({ json: data })),
    onSuccess: (created) => {
      // prime detail cache
      qc.setQueryData(keys.detail(created.id), created);
      // refresh lists and count
      qc.invalidateQueries({ queryKey: keys.all });
      qc.invalidateQueries({ queryKey: keys.count() });
    },
  });
}

function useUpdate() {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: SiteUpdateInput }) =>
      jsonOrThrow<PrivateDTO>(
        apiClient.api.sites[":id"].$patch({ param: { id }, json: data }),
      ),
    onMutate: async ({ id, data }) => {
      // cancel in-flight queries
      await qc.cancelQueries({ queryKey: keys.detail(id) });
      await qc.cancelQueries({ queryKey: keys.all });

      const prevDetail = qc.getQueryData<PrivateDTO>(keys.detail(id));
      if (prevDetail) {
        qc.setQueryData<PrivateDTO>(keys.detail(id), {
          ...prevDetail,
          ...data,
          updatedAt: new Date(),
        });
      }

      const lists = qc.getQueriesData<ListResp>({ queryKey: keys.all });
      lists.forEach(([k, snapshot]) => {
        if (!snapshot) return;
        qc.setQueryData<ListResp>(k, {
          ...snapshot,
          items: snapshot.items.map((s) =>
            s.id === id ? { ...s, ...data, updatedAt: new Date() } : s,
          ),
        });
      });

      return { prevDetail, lists };
    },
    onError: (_e, { id }, ctx) => {
      if (ctx?.prevDetail) qc.setQueryData(keys.detail(id), ctx.prevDetail);
      ctx?.lists?.forEach(([k, snap]) => {
        qc.setQueryData(k, snap);
      });
    },
    onSettled: (_res, _err, vars) => {
      qc.invalidateQueries({ queryKey: keys.detail(vars.id) });
      qc.invalidateQueries({ queryKey: keys.all });
    },
  });
}

function useDelete() {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: async ({ id }: { id: string }) => {
      const res = await apiClient.api.sites[":id"].$delete({ param: { id } });
      if (!res.ok) {
        const msg = await res.text().catch(() => "");
        throw new Error(msg || `HTTP ${res.status}`);
      }
      // 204, no body
    },
    onMutate: async ({ id }) => {
      await qc.cancelQueries({ queryKey: keys.all });
      await qc.cancelQueries({ queryKey: keys.detail(id) });

      const lists = qc.getQueriesData<ListResp>({ queryKey: keys.all });
      lists.forEach(([k, snapshot]) => {
        if (!snapshot) return;
        qc.setQueryData<ListResp>(k, {
          total: Math.max(0, snapshot.total - 1),
          items: snapshot.items.filter((s) => s.id !== id),
        });
      });

      qc.removeQueries({ queryKey: keys.detail(id) });

      return { lists };
    },
    onError: (_e, _vars, ctx) => {
      ctx?.lists?.forEach(([k, snap]) => {
        qc.setQueryData(k, snap);
      });
    },
    onSettled: () => {
      qc.invalidateQueries({ queryKey: keys.all });
      qc.invalidateQueries({ queryKey: keys.count() });
    },
  });
}

export const siteClient = {
  keys,
  queries: {
    useAll,
    useCount,
    useGet,
    useGetBySubdomain,
  },
  mutations: {
    useCreate,
    useUpdate,
    useDelete,
  },
};
