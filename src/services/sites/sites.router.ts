import { zValidator } from "@hono/zod-validator";
import { Hono } from "hono";
import { HTTPException } from "hono/http-exception";
import {
  SiteCreateInput,
  SiteIdParam,
  SiteListQuery,
  SiteSubParam,
  SiteUpdateInput,
} from "@/services/sites/sites.schema";
import { SiteService } from "./sites.service";

function assert<T>(
  value: T | null | false,
  status = 404 as const,
  message = "Not found",
): T {
  if (!value) throw new HTTPException(status, { message });
  return value;
}

export const sitesRouter = new Hono()
  .get(
    "/",
    zValidator("query", SiteListQuery, (result) => {
      if (!result.success) {
        throw new HTTPException(400, { message: result.error.message });
      }
    }),
    async (c) => {
      const [items, total] = await Promise.all([
        SiteService.queries.getAll(),
        SiteService.queries.count(),
      ]);
      return c.json({ items, total });
    },
  )
  .get("/count", async (c) => {
    const total = await SiteService.queries.count();
    return c.json({ total });
  })
  .get(
    "/:id",
    zValidator("param", SiteIdParam, (result) => {
      if (!result.success)
        throw new HTTPException(400, { message: result.error.message });
    }),
    async (c) => {
      const { id } = c.req.valid("param");
      const site = await SiteService.queries.get({ id });
      return c.json(assert(site)); // 404 if null
    },
  )
  .get(
    "/by-subdomain/:sub",
    zValidator("param", SiteSubParam, (result) => {
      if (!result.success)
        throw new HTTPException(400, { message: result.error.message });
    }),
    async (c) => {
      const { sub } = c.req.valid("param");
      const site = await SiteService.queries.getBySubdomain({ subdomain: sub });
      return c.json(assert(site), 200, {
        "Cache-Control": "public, s-maxage=60, stale-while-revalidate=300",
      });
    },
  )
  .post(
    "/",
    zValidator("json", SiteCreateInput, (result) => {
      if (!result.success)
        throw new HTTPException(400, { message: result.error.message });
    }),
    async (c) => {
      const body = c.req.valid("json");
      const created = await SiteService.mutations.create({ data: body });
      // service returns null when unauthorized; respond 401 instead of 400
      if (!created) throw new HTTPException(401, { message: "Unauthorized" });
      return c.json(created, 201);
    },
  )
  .patch(
    "/:id",
    zValidator("param", SiteIdParam),
    zValidator("json", SiteUpdateInput),
    async (c) => {
      const { id } = c.req.valid("param");
      const patch = c.req.valid("json");
      const updated = await SiteService.mutations.update({ id, data: patch });
      if (!updated)
        throw new HTTPException(404, { message: "Not found or unauthorized" });
      return c.json(updated);
    },
  )
  .delete("/:id", zValidator("param", SiteIdParam), async (c) => {
    const { id } = c.req.valid("param");
    const ok = await SiteService.mutations.delete({ id });
    if (!ok)
      throw new HTTPException(404, { message: "Not found or unauthorized" });
    return c.newResponse(null, 204);
  });
