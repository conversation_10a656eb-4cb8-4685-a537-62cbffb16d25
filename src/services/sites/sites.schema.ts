import { z } from "zod";

export const SiteIdParam = z.object({
  id: z.string().min(1, "id required"),
});

export const SiteSubParam = z.object({
  sub: z
    .string()
    .min(1)
    .regex(/^[a-z0-9-]+$/, "lowercase letters, numbers and dashes only"),
});

export const SiteCreateInput = z.object({
  name: z.string().min(2).max(100),
  subdomain: z
    .string()
    .min(2)
    .max(63)
    .regex(/^[a-z0-9-]+$/, "lowercase letters, numbers and dashes only"),
});

export const SiteUpdateInput = z
  .object({
    name: z.string().min(2).max(100).optional(),
    subdomain: z
      .string()
      .min(2)
      .max(63)
      .regex(/^[a-z0-9-]+$/, "lowercase letters, numbers and dashes only")
      .optional(),
  })
  .refine((obj) => Object.keys(obj).length > 0, {
    message: "provide at least one field",
  });

export const SiteListQuery = z.object({
  q: z.string().optional(),
});
