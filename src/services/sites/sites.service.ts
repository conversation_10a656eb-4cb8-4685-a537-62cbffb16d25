import "server-only";

import { headers } from "next/headers";
import type { Site } from "@/generated/prisma";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import type {
  DTO,
  ISitesMutations,
  ISitesQueries,
  PublicDTO,
} from "./sites.types";

const toDTO = (site: Site): DTO => {
  return {
    id: site.id,
    name: site.name,
    subdomain: site.subdomain,
    createdAt: site.createdAt,
    updatedAt: site.updatedAt,
  };
};

const toPublicDTO = (s: Site): PublicDTO => ({
  subdomain: s.subdomain,
  name: s.name,
});

const queries: ISitesQueries = {
  async count() {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return 0;
    }

    return await db.site.count({
      where: { userId: session.user.id },
    });
  },
  async getAll() {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return [];
    }

    const sites = await db.site.findMany({
      where: { userId: session.user.id },
    });

    return sites.map(toDTO);
  },
  async get({ id }) {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return null;
    }

    const site = await db.site.findUnique({
      where: { id, userId: session.user.id },
    });

    return site ? toDTO(site) : null;
  },
  async getBySubdomain({ subdomain }) {
    const site = await db.site.findUnique({
      where: { subdomain },
    });

    return site ? toPublicDTO(site) : null;
  },
};

const mutations: ISitesMutations = {
  async create({ data }) {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return null;
    }

    const site = await db.site.create({
      data: { ...data, userId: session.user.id },
    });

    return toDTO(site);
  },
  async delete({ id }) {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return false;
    }

    await db.site.delete({
      where: { id, userId: session.user.id },
    });

    return true;
  },
  async update({ id, data }) {
    const session = await auth.api.getSession({ headers: await headers() });

    if (!session?.user) {
      return null;
    }

    const site = await db.site.update({
      where: { id, userId: session.user.id },
      data,
    });

    return toDTO(site);
  },
};

export const SiteService = { queries, mutations };
